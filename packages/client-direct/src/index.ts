import express, { NextFunction, RequestHandler } from "express";
import { setupExpressApp } from "./server/express-setup";
import { Request, Response } from "express";
import { IDirectClient } from "./models/types";
import { messageHandlerTemplate } from "./templates/message-templates";
import { startServer, stopServer } from "./server/server";
import { registerAgent, unregisterAgent } from "./services/agent-service";
import {
    handleMessageRequest,
    handleImageRequest,
    handleImageGenRequest,
    handleStopAgent,
    handleRecraftImageRequest,
    handleRecraftImageToImageRequest,
    handleRecraftVectorizeRequest,
    handleRecraftRemoveBackgroundRequest,
} from "./handlers/agent-handlers";
import {
    handleStartLinkedInAgent,
    handleLinkedInCallback,
} from "./handlers/linkedin-handlers";
import { upload } from "./utils/upload-config";
import {
    handleStartTwitterAgent,
    handleTwitterCallback,
    handleCheckTwitterPremium,
    handleStartTwitterBotAgent,
} from "./handlers/twitter-handlers";
import {
    handleFineTunePost,
    handleFineTuneGet,
} from "./handlers/fine-tune-handlers";
import { AgentRuntime } from "@elizaos/core";
import { Client, IAgentRuntime } from "@elizaos/core";
import { settings } from "@elizaos/core";
import {
    handleInstagramCallback,
    handleStartInstagramAgent,
} from "./handlers/instagram-handlers";

export class DirectClient implements IDirectClient {
    public app: express.Application;
    public agents: Map<string, AgentRuntime>; // container management
    public server: any; // Store server instance
    public startTwitterAgent: Function;
    public startLinkedInAgent: Function;
    public startInstagramAgent: Function;
    public db: any;

    constructor() {
        this.agents = new Map();
        this.app = setupExpressApp(this.agents, this);

        this.app.post(
            "/:agentId/message",
            upload.single("file"),
            async (req, res) =>
                await handleMessageRequest(this.agents, req, res)
        );
        this.app.post(
            "/:agentId/image",
            async (req, res) => await handleImageRequest(this.agents, req, res)
        );
        this.app.post(
            "/:agentId/post-image-gen",
            async (req, res) =>
                await handleImageGenRequest(this.agents, req, res)
        );

        // Recraft AI image generation endpoints
        this.app.post(
            "/:agentId/recraft-image",
            async (req, res) =>
                await handleRecraftImageRequest(this.agents, req, res)
        );
        this.app.post(
            "/:agentId/recraft-image-to-image",
            upload.single("image"),
            async (req, res) =>
                await handleRecraftImageToImageRequest(this.agents, req, res)
        );
        this.app.post(
            "/:agentId/recraft-vectorize",
            upload.single("image"),
            async (req, res) =>
                await handleRecraftVectorizeRequest(this.agents, req, res)
        );
        this.app.post(
            "/:agentId/recraft-remove-background",
            upload.single("image"),
            async (req, res) =>
                await handleRecraftRemoveBackgroundRequest(
                    this.agents,
                    req,
                    res
                )
        );

        this.app.post("/fine-tune", handleFineTunePost);
        this.app.get("/fine-tune/:assetId", handleFineTuneGet);
        this.app.post("/start-twitter-agent", (async (
            req: Request,
            res: Response,
            next: NextFunction
        ) => {
            try {
                await handleStartTwitterAgent(req, res, this);
            } catch (error) {
                next(error);
            }
        }) as RequestHandler);

        this.app.get(
            "/twitter-auth/callback",
            async (req, res) => await handleTwitterCallback(req, res, this)
        );
        this.app.post("/check-twitter-premium", (async (
            req: Request,
            res: Response,
            next: NextFunction
        ) => {
            try {
                await handleCheckTwitterPremium(req, res, this);
            } catch (error) {
                next(error);
            }
        }) as RequestHandler);
        this.app.post("/start-twitter-bot-agent", (async (
            req: Request,
            res: Response,
            next: NextFunction
        ) => {
            try {
                await handleStartTwitterBotAgent(req, res, this);
            } catch (error) {
                next(error);
            }
        }) as RequestHandler);

        this.app.post("/start-linkedin-agent", (async (
            req: Request,
            res: Response,
            next: NextFunction
        ) => {
            try {
                await handleStartLinkedInAgent(req, res, this);
            } catch (error) {
                next(error);
            }
        }) as RequestHandler);
        this.app.get(
            "/linkedin-auth/callback",
            async (req, res) => await handleLinkedInCallback(req, res, this)
        );
        this.app.post("/start-instagram-agent", (async (
            req: Request,
            res: Response,
            next: NextFunction
        ) => {
            try {
                await handleStartInstagramAgent(req, res, this);
            } catch (error) {
                next(error);
            }
        }) as RequestHandler);
        this.app.get(
            "/instagram-auth/callback",
            async (req, res) => await handleInstagramCallback(req, res, this)
        );
        this.app.post(
            "/stop-agent/:agentId",
            async (req, res) =>
                await handleStopAgent(
                    this.agents,
                    this.db,
                    req.params.agentId,
                    this.unregisterAgent.bind(this),
                    req,
                    res
                )
        );
    }

    public registerAgent(runtime: AgentRuntime): void {
        registerAgent(this.agents, runtime);
    }

    public unregisterAgent(runtime: AgentRuntime): void {
        unregisterAgent(this.agents, runtime);
    }

    public start(port: number): void {
        this.server = startServer(this.app, port);
    }

    public stop(): void {
        stopServer(this.server);
    }
}

export const DirectClientInterface: Client = {
    start: async (_runtime: IAgentRuntime) => {
        const client = new DirectClient();
        const serverPort = parseInt(settings.SERVER_PORT || "3000");
        client.start(serverPort);
        return client;
    },
    stop: async (_runtime: IAgentRuntime, client?: Client) => {
        if (client instanceof DirectClient) {
            client.stop();
        }
    },
};

export { messageHandlerTemplate };
export { saveBase64Image } from "./utils/file-utils";
export default DirectClientInterface;
